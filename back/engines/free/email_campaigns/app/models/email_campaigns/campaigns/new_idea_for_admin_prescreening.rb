# frozen_string_literal: true

# == Schema Information
#
# Table name: email_campaigns_campaigns
#
#  id                   :uuid             not null, primary key
#  type                 :string           not null
#  author_id            :uuid
#  enabled              :boolean
#  sender               :string
#  reply_to             :string
#  schedule             :jsonb
#  subject_multiloc     :jsonb
#  body_multiloc        :jsonb
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  deliveries_count     :integer          default(0), not null
#  context_id           :uuid
#  title_multiloc       :jsonb
#  intro_multiloc       :jsonb
#  button_text_multiloc :jsonb
#  context_type         :string
#
# Indexes
#
#  index_email_campaigns_campaigns_on_author_id   (author_id)
#  index_email_campaigns_campaigns_on_context_id  (context_id)
#  index_email_campaigns_campaigns_on_type        (type)
#
# Foreign Keys
#
#  fk_rails_...  (author_id => users.id)
#
module EmailCampaigns
  class Campaigns::NewIdeaForAdminPrescreening < Campaigns::NewIdeaForAdminBase
    allow_lifecycle_stages only: %w[trial active]
    filter :prescreening_only?

    def mailer_class
      NewIdeaForAdminPrescreeningMailer
    end

    def self.trigger_multiloc_key
      'email_campaigns.admin_labels.trigger.new_input_awaits_screening'
    end

    private

    def prescreening_only?(activity:, time: nil)
      return false unless activity&.item.is_a?(::Idea)

      !activity.item.published?
    end
  end
end
