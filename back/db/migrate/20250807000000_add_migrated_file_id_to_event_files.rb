# frozen_string_literal: true

class AddMigratedFileIdToContainerFiles < ActiveRecord::Migration[7.1]
  def change
    # Adding column to mark migrated files across all container file tables
    container_file_tables = %i[
      event_files
      phase_files
      project_files
      idea_files
      project_folders_files
      static_page_files
    ]

    container_file_tables.each do |table|
      add_reference table, :migrated_file,
        foreign_key: { to_table: :files },
        type: :uuid, null: true, index: true,
        comment: 'References the Files::File record after migration to new file system'
    end
  end
end
