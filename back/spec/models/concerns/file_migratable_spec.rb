# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FileMigratable do
  let(:event) { create(:event) }
  let(:migrated_file) { create(:file) }

  describe 'default scope' do
    let!(:non_migrated_file) { create(:event_file, event: event) }
    let!(:migrated_file_record) do
      create(:event_file, event: event, migrated_file: migrated_file)
    end

    it 'only returns non-migrated files by default' do
      results = EventFile.all
      expect(results).to include(non_migrated_file)
      expect(results).not_to include(migrated_file_record)
    end

    it 'can access migrated files with unscoped' do
      results = EventFile.unscoped
      expect(results).to include(non_migrated_file)
      expect(results).to include(migrated_file_record)
    end
  end

  describe '#migrated?' do
    context 'when migrated_file_id is nil' do
      let(:file_record) { build(:event_file, migrated_file_id: nil) }

      it 'returns false' do
        expect(file_record.migrated?).to be false
      end
    end

    context 'when migrated_file_id is present' do
      let(:file_record) { build(:event_file, migrated_file_id: migrated_file.id) }

      it 'returns true' do
        expect(file_record.migrated?).to be true
      end
    end
  end

  describe 'migrated_file association' do
    let(:file_record) { create(:event_file, event: event) }

    it 'can be associated with a Files::File' do
      file_record.migrated_file = migrated_file
      expect(file_record.migrated_file).to eq(migrated_file)
    end

    it 'is optional' do
      expect(file_record.migrated_file).to be_nil
      expect(file_record).to be_valid
    end
  end

  describe 'concern inclusion in models' do
    it 'is included in EventFile' do
      expect(EventFile.included_modules).to include(FileMigratable)
    end

    it 'is included in PhaseFile' do
      expect(PhaseFile.included_modules).to include(FileMigratable)
    end

    it 'is included in ProjectFile' do
      expect(ProjectFile.included_modules).to include(FileMigratable)
    end

    it 'is included in IdeaFile' do
      expect(IdeaFile.included_modules).to include(FileMigratable)
    end

    it 'is included in ProjectFolders::File' do
      expect(ProjectFolders::File.included_modules).to include(FileMigratable)
    end

    it 'is included in StaticPageFile' do
      expect(StaticPageFile.included_modules).to include(FileMigratable)
    end
  end

  describe 'model behavior consistency' do
    let(:models_to_test) do
      [
        { class: EventFile, factory: :event_file },
        { class: PhaseFile, factory: :phase_file },
        { class: ProjectFile, factory: :project_file },
        { class: IdeaFile, factory: :idea_file },
        { class: StaticPageFile, factory: :static_page_file }
      ]
    end

    it 'all models have the migrated? method' do
      models_to_test.each do |model_info|
        instance = build(model_info[:factory])
        expect(instance).to respond_to(:migrated?)
      end
    end

    it 'all models have the migrated_file association' do
      models_to_test.each do |model_info|
        instance = build(model_info[:factory])
        expect(instance).to respond_to(:migrated_file)
        expect(instance).to respond_to(:migrated_file=)
      end
    end

    it 'all models apply the default scope' do
      models_to_test.each do |model_info|
        model_class = model_info[:class]
        # Check that the default scope includes the where clause for migrated_file_id
        sql = model_class.all.to_sql
        expect(sql).to include('migrated_file_id')
        expect(sql).to include('IS NULL')
      end
    end
  end
end
