# frozen_string_literal: true

# Concern for container file models that support migration to the new Files::File system
# This concern provides:
# - Default scope to exclude migrated files from queries
# - Migration status checking method
# - Association to the migrated Files::File record
module FileMigratable
  extend ActiveSupport::Concern

  included do
    # Only show non-migrated files by default
    default_scope { where(migrated_file_id: nil) }

    # Association to the new Files::File record after migration
    belongs_to :migrated_file, class_name: 'Files::File', optional: true
  end

  # Check if this file has been migrated to the new system
  # @return [Boolean] true if the file has been migrated
  def migrated?
    migrated_file_id.present?
  end
end
