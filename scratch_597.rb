# events that have event files
Event.where.associated(:event_files)

# events that don't have event files
# using .missing
# https://guides.rubyonrails.org/active_record_querying.html

event_with_files = Event.where.associated(:event_files)

event_with_file.find_each do |event|
  Files::File.transactions do
    event.event_files.find_each do |event_file|
      file = Files::File.new(
        name: event_file.name,
        content: event_file.file,
        uploader: get_legacy_file_uploader(event_file),
        created_at: event_file.created_at,
      )

      file.attachments.build(position: event_file.ordering)
      file.
    end
  end
end