RubyLLM.configure do |config|
  # Azure OpenAI
  config.openai_api_base = "#{ENV['AZURE_OPENAI_URI']}/openai/v1"
  config.openai_api_key = ENV['AZURE_OPENAI_API_KEY']
end

# Patch RubyLLM::Providers::OpenAI::Chat#completion_url
module Ruby<PERSON><PERSON>
  module Providers
    module OpenAI
      module Chat
        def completion_url
          'chat/completions?api-version=preview'
        end
      end
    end
  end
end

begin
  chat = RubyLLM::Chat.new(model: 'gpt-4.1')
  response = chat.ask('Hello, how are you?')
  rescue StandardError => e
end


# Alternative with OpenAI::Client

client = OpenAI::Client.new(
  access_token: ENV.fetch('AZURE_OPENAI_API_KEY'),
  uri_base: "#{ENV.fetch('AZURE_OPENAI_URI')}/openai/v1",
  api_type: :azure,
  api_version: 'preview',
  request_timeout: 900,
)

begin
response = client.chat(
  parameters: {
    model: 'gpt-4.1',
    messages: [{ role: 'user', content: "What were the main action items discussed?" }],
    temperature: 0.1
  }
)
rescue StandardError => e
end

